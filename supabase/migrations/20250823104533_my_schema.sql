drop extension if exists "pgaudit";

revoke delete on table "public"."base_rank" from "anon";

revoke insert on table "public"."base_rank" from "anon";

revoke references on table "public"."base_rank" from "anon";

revoke select on table "public"."base_rank" from "anon";

revoke trigger on table "public"."base_rank" from "anon";

revoke truncate on table "public"."base_rank" from "anon";

revoke update on table "public"."base_rank" from "anon";

revoke delete on table "public"."base_rank" from "authenticated";

revoke insert on table "public"."base_rank" from "authenticated";

revoke references on table "public"."base_rank" from "authenticated";

revoke select on table "public"."base_rank" from "authenticated";

revoke trigger on table "public"."base_rank" from "authenticated";

revoke truncate on table "public"."base_rank" from "authenticated";

revoke update on table "public"."base_rank" from "authenticated";

revoke delete on table "public"."base_rank" from "service_role";

revoke insert on table "public"."base_rank" from "service_role";

revoke references on table "public"."base_rank" from "service_role";

revoke select on table "public"."base_rank" from "service_role";

revoke trigger on table "public"."base_rank" from "service_role";

revoke truncate on table "public"."base_rank" from "service_role";

revoke update on table "public"."base_rank" from "service_role";

revoke delete on table "public"."contract" from "anon";

revoke insert on table "public"."contract" from "anon";

revoke references on table "public"."contract" from "anon";

revoke select on table "public"."contract" from "anon";

revoke trigger on table "public"."contract" from "anon";

revoke truncate on table "public"."contract" from "anon";

revoke update on table "public"."contract" from "anon";

revoke delete on table "public"."contract" from "authenticated";

revoke insert on table "public"."contract" from "authenticated";

revoke references on table "public"."contract" from "authenticated";

revoke select on table "public"."contract" from "authenticated";

revoke trigger on table "public"."contract" from "authenticated";

revoke truncate on table "public"."contract" from "authenticated";

revoke update on table "public"."contract" from "authenticated";

revoke delete on table "public"."contract" from "service_role";

revoke insert on table "public"."contract" from "service_role";

revoke references on table "public"."contract" from "service_role";

revoke select on table "public"."contract" from "service_role";

revoke trigger on table "public"."contract" from "service_role";

revoke truncate on table "public"."contract" from "service_role";

revoke update on table "public"."contract" from "service_role";

revoke delete on table "public"."debt_root" from "anon";

revoke insert on table "public"."debt_root" from "anon";

revoke references on table "public"."debt_root" from "anon";

revoke select on table "public"."debt_root" from "anon";

revoke trigger on table "public"."debt_root" from "anon";

revoke truncate on table "public"."debt_root" from "anon";

revoke update on table "public"."debt_root" from "anon";

revoke delete on table "public"."debt_root" from "authenticated";

revoke insert on table "public"."debt_root" from "authenticated";

revoke references on table "public"."debt_root" from "authenticated";

revoke select on table "public"."debt_root" from "authenticated";

revoke trigger on table "public"."debt_root" from "authenticated";

revoke truncate on table "public"."debt_root" from "authenticated";

revoke update on table "public"."debt_root" from "authenticated";

revoke delete on table "public"."debt_root" from "service_role";

revoke insert on table "public"."debt_root" from "service_role";

revoke references on table "public"."debt_root" from "service_role";

revoke select on table "public"."debt_root" from "service_role";

revoke trigger on table "public"."debt_root" from "service_role";

revoke truncate on table "public"."debt_root" from "service_role";

revoke update on table "public"."debt_root" from "service_role";

revoke delete on table "public"."lead" from "anon";

revoke insert on table "public"."lead" from "anon";

revoke references on table "public"."lead" from "anon";

revoke select on table "public"."lead" from "anon";

revoke trigger on table "public"."lead" from "anon";

revoke truncate on table "public"."lead" from "anon";

revoke update on table "public"."lead" from "anon";

revoke delete on table "public"."lead" from "authenticated";

revoke insert on table "public"."lead" from "authenticated";

revoke references on table "public"."lead" from "authenticated";

revoke select on table "public"."lead" from "authenticated";

revoke trigger on table "public"."lead" from "authenticated";

revoke truncate on table "public"."lead" from "authenticated";

revoke update on table "public"."lead" from "authenticated";

revoke delete on table "public"."lead" from "service_role";

revoke insert on table "public"."lead" from "service_role";

revoke references on table "public"."lead" from "service_role";

revoke select on table "public"."lead" from "service_role";

revoke trigger on table "public"."lead" from "service_role";

revoke truncate on table "public"."lead" from "service_role";

revoke update on table "public"."lead" from "service_role";

revoke delete on table "public"."pack" from "anon";

revoke insert on table "public"."pack" from "anon";

revoke references on table "public"."pack" from "anon";

revoke select on table "public"."pack" from "anon";

revoke trigger on table "public"."pack" from "anon";

revoke truncate on table "public"."pack" from "anon";

revoke update on table "public"."pack" from "anon";

revoke delete on table "public"."pack" from "authenticated";

revoke insert on table "public"."pack" from "authenticated";

revoke references on table "public"."pack" from "authenticated";

revoke select on table "public"."pack" from "authenticated";

revoke trigger on table "public"."pack" from "authenticated";

revoke truncate on table "public"."pack" from "authenticated";

revoke update on table "public"."pack" from "authenticated";

revoke delete on table "public"."pack" from "service_role";

revoke insert on table "public"."pack" from "service_role";

revoke references on table "public"."pack" from "service_role";

revoke select on table "public"."pack" from "service_role";

revoke trigger on table "public"."pack" from "service_role";

revoke truncate on table "public"."pack" from "service_role";

revoke update on table "public"."pack" from "service_role";

revoke delete on table "public"."pack_profile" from "anon";

revoke insert on table "public"."pack_profile" from "anon";

revoke references on table "public"."pack_profile" from "anon";

revoke select on table "public"."pack_profile" from "anon";

revoke trigger on table "public"."pack_profile" from "anon";

revoke truncate on table "public"."pack_profile" from "anon";

revoke update on table "public"."pack_profile" from "anon";

revoke delete on table "public"."pack_profile" from "authenticated";

revoke insert on table "public"."pack_profile" from "authenticated";

revoke references on table "public"."pack_profile" from "authenticated";

revoke select on table "public"."pack_profile" from "authenticated";

revoke trigger on table "public"."pack_profile" from "authenticated";

revoke truncate on table "public"."pack_profile" from "authenticated";

revoke update on table "public"."pack_profile" from "authenticated";

revoke delete on table "public"."pack_profile" from "service_role";

revoke insert on table "public"."pack_profile" from "service_role";

revoke references on table "public"."pack_profile" from "service_role";

revoke select on table "public"."pack_profile" from "service_role";

revoke trigger on table "public"."pack_profile" from "service_role";

revoke truncate on table "public"."pack_profile" from "service_role";

revoke update on table "public"."pack_profile" from "service_role";

revoke delete on table "public"."payment" from "anon";

revoke insert on table "public"."payment" from "anon";

revoke references on table "public"."payment" from "anon";

revoke select on table "public"."payment" from "anon";

revoke trigger on table "public"."payment" from "anon";

revoke truncate on table "public"."payment" from "anon";

revoke update on table "public"."payment" from "anon";

revoke delete on table "public"."payment" from "authenticated";

revoke insert on table "public"."payment" from "authenticated";

revoke references on table "public"."payment" from "authenticated";

revoke select on table "public"."payment" from "authenticated";

revoke trigger on table "public"."payment" from "authenticated";

revoke truncate on table "public"."payment" from "authenticated";

revoke update on table "public"."payment" from "authenticated";

revoke delete on table "public"."payment" from "service_role";

revoke insert on table "public"."payment" from "service_role";

revoke references on table "public"."payment" from "service_role";

revoke select on table "public"."payment" from "service_role";

revoke trigger on table "public"."payment" from "service_role";

revoke truncate on table "public"."payment" from "service_role";

revoke update on table "public"."payment" from "service_role";

revoke delete on table "public"."rank" from "anon";

revoke insert on table "public"."rank" from "anon";

revoke references on table "public"."rank" from "anon";

revoke select on table "public"."rank" from "anon";

revoke trigger on table "public"."rank" from "anon";

revoke truncate on table "public"."rank" from "anon";

revoke update on table "public"."rank" from "anon";

revoke delete on table "public"."rank" from "authenticated";

revoke insert on table "public"."rank" from "authenticated";

revoke references on table "public"."rank" from "authenticated";

revoke select on table "public"."rank" from "authenticated";

revoke trigger on table "public"."rank" from "authenticated";

revoke truncate on table "public"."rank" from "authenticated";

revoke update on table "public"."rank" from "authenticated";

revoke delete on table "public"."rank" from "service_role";

revoke insert on table "public"."rank" from "service_role";

revoke references on table "public"."rank" from "service_role";

revoke select on table "public"."rank" from "service_role";

revoke trigger on table "public"."rank" from "service_role";

revoke truncate on table "public"."rank" from "service_role";

revoke update on table "public"."rank" from "service_role";

revoke delete on table "public"."rank_point" from "anon";

revoke insert on table "public"."rank_point" from "anon";

revoke references on table "public"."rank_point" from "anon";

revoke select on table "public"."rank_point" from "anon";

revoke trigger on table "public"."rank_point" from "anon";

revoke truncate on table "public"."rank_point" from "anon";

revoke update on table "public"."rank_point" from "anon";

revoke delete on table "public"."rank_point" from "authenticated";

revoke insert on table "public"."rank_point" from "authenticated";

revoke references on table "public"."rank_point" from "authenticated";

revoke select on table "public"."rank_point" from "authenticated";

revoke trigger on table "public"."rank_point" from "authenticated";

revoke truncate on table "public"."rank_point" from "authenticated";

revoke update on table "public"."rank_point" from "authenticated";

revoke delete on table "public"."rank_point" from "service_role";

revoke insert on table "public"."rank_point" from "service_role";

revoke references on table "public"."rank_point" from "service_role";

revoke select on table "public"."rank_point" from "service_role";

revoke trigger on table "public"."rank_point" from "service_role";

revoke truncate on table "public"."rank_point" from "service_role";

revoke update on table "public"."rank_point" from "service_role";

revoke delete on table "public"."rank_school" from "anon";

revoke insert on table "public"."rank_school" from "anon";

revoke references on table "public"."rank_school" from "anon";

revoke select on table "public"."rank_school" from "anon";

revoke trigger on table "public"."rank_school" from "anon";

revoke truncate on table "public"."rank_school" from "anon";

revoke update on table "public"."rank_school" from "anon";

revoke delete on table "public"."rank_school" from "authenticated";

revoke insert on table "public"."rank_school" from "authenticated";

revoke references on table "public"."rank_school" from "authenticated";

revoke select on table "public"."rank_school" from "authenticated";

revoke trigger on table "public"."rank_school" from "authenticated";

revoke truncate on table "public"."rank_school" from "authenticated";

revoke update on table "public"."rank_school" from "authenticated";

revoke delete on table "public"."rank_school" from "service_role";

revoke insert on table "public"."rank_school" from "service_role";

revoke references on table "public"."rank_school" from "service_role";

revoke select on table "public"."rank_school" from "service_role";

revoke trigger on table "public"."rank_school" from "service_role";

revoke truncate on table "public"."rank_school" from "service_role";

revoke update on table "public"."rank_school" from "service_role";

revoke delete on table "public"."rank_student" from "anon";

revoke insert on table "public"."rank_student" from "anon";

revoke references on table "public"."rank_student" from "anon";

revoke select on table "public"."rank_student" from "anon";

revoke trigger on table "public"."rank_student" from "anon";

revoke truncate on table "public"."rank_student" from "anon";

revoke update on table "public"."rank_student" from "anon";

revoke delete on table "public"."rank_student" from "authenticated";

revoke insert on table "public"."rank_student" from "authenticated";

revoke references on table "public"."rank_student" from "authenticated";

revoke select on table "public"."rank_student" from "authenticated";

revoke trigger on table "public"."rank_student" from "authenticated";

revoke truncate on table "public"."rank_student" from "authenticated";

revoke update on table "public"."rank_student" from "authenticated";

revoke delete on table "public"."rank_student" from "service_role";

revoke insert on table "public"."rank_student" from "service_role";

revoke references on table "public"."rank_student" from "service_role";

revoke select on table "public"."rank_student" from "service_role";

revoke trigger on table "public"."rank_student" from "service_role";

revoke truncate on table "public"."rank_student" from "service_role";

revoke update on table "public"."rank_student" from "service_role";

revoke delete on table "public"."sale" from "anon";

revoke insert on table "public"."sale" from "anon";

revoke references on table "public"."sale" from "anon";

revoke select on table "public"."sale" from "anon";

revoke trigger on table "public"."sale" from "anon";

revoke truncate on table "public"."sale" from "anon";

revoke update on table "public"."sale" from "anon";

revoke delete on table "public"."sale" from "authenticated";

revoke insert on table "public"."sale" from "authenticated";

revoke references on table "public"."sale" from "authenticated";

revoke select on table "public"."sale" from "authenticated";

revoke trigger on table "public"."sale" from "authenticated";

revoke truncate on table "public"."sale" from "authenticated";

revoke update on table "public"."sale" from "authenticated";

revoke delete on table "public"."sale" from "service_role";

revoke insert on table "public"."sale" from "service_role";

revoke references on table "public"."sale" from "service_role";

revoke select on table "public"."sale" from "service_role";

revoke trigger on table "public"."sale" from "service_role";

revoke truncate on table "public"."sale" from "service_role";

revoke update on table "public"."sale" from "service_role";

revoke delete on table "public"."sale_root" from "anon";

revoke insert on table "public"."sale_root" from "anon";

revoke references on table "public"."sale_root" from "anon";

revoke select on table "public"."sale_root" from "anon";

revoke trigger on table "public"."sale_root" from "anon";

revoke truncate on table "public"."sale_root" from "anon";

revoke update on table "public"."sale_root" from "anon";

revoke delete on table "public"."sale_root" from "authenticated";

revoke insert on table "public"."sale_root" from "authenticated";

revoke references on table "public"."sale_root" from "authenticated";

revoke select on table "public"."sale_root" from "authenticated";

revoke trigger on table "public"."sale_root" from "authenticated";

revoke truncate on table "public"."sale_root" from "authenticated";

revoke update on table "public"."sale_root" from "authenticated";

revoke delete on table "public"."sale_root" from "service_role";

revoke insert on table "public"."sale_root" from "service_role";

revoke references on table "public"."sale_root" from "service_role";

revoke select on table "public"."sale_root" from "service_role";

revoke trigger on table "public"."sale_root" from "service_role";

revoke truncate on table "public"."sale_root" from "service_role";

revoke update on table "public"."sale_root" from "service_role";

revoke delete on table "public"."school_profile" from "anon";

revoke insert on table "public"."school_profile" from "anon";

revoke references on table "public"."school_profile" from "anon";

revoke select on table "public"."school_profile" from "anon";

revoke trigger on table "public"."school_profile" from "anon";

revoke truncate on table "public"."school_profile" from "anon";

revoke update on table "public"."school_profile" from "anon";

revoke delete on table "public"."school_profile" from "authenticated";

revoke insert on table "public"."school_profile" from "authenticated";

revoke references on table "public"."school_profile" from "authenticated";

revoke select on table "public"."school_profile" from "authenticated";

revoke trigger on table "public"."school_profile" from "authenticated";

revoke truncate on table "public"."school_profile" from "authenticated";

revoke update on table "public"."school_profile" from "authenticated";

revoke delete on table "public"."school_profile" from "service_role";

revoke insert on table "public"."school_profile" from "service_role";

revoke references on table "public"."school_profile" from "service_role";

revoke select on table "public"."school_profile" from "service_role";

revoke trigger on table "public"."school_profile" from "service_role";

revoke truncate on table "public"."school_profile" from "service_role";

revoke update on table "public"."school_profile" from "service_role";

revoke delete on table "public"."school_settings" from "anon";

revoke insert on table "public"."school_settings" from "anon";

revoke references on table "public"."school_settings" from "anon";

revoke select on table "public"."school_settings" from "anon";

revoke trigger on table "public"."school_settings" from "anon";

revoke truncate on table "public"."school_settings" from "anon";

revoke update on table "public"."school_settings" from "anon";

revoke delete on table "public"."school_settings" from "authenticated";

revoke insert on table "public"."school_settings" from "authenticated";

revoke references on table "public"."school_settings" from "authenticated";

revoke select on table "public"."school_settings" from "authenticated";

revoke trigger on table "public"."school_settings" from "authenticated";

revoke truncate on table "public"."school_settings" from "authenticated";

revoke update on table "public"."school_settings" from "authenticated";

revoke delete on table "public"."school_settings" from "service_role";

revoke insert on table "public"."school_settings" from "service_role";

revoke references on table "public"."school_settings" from "service_role";

revoke select on table "public"."school_settings" from "service_role";

revoke trigger on table "public"."school_settings" from "service_role";

revoke truncate on table "public"."school_settings" from "service_role";

revoke update on table "public"."school_settings" from "service_role";

revoke delete on table "public"."service_pack" from "anon";

revoke insert on table "public"."service_pack" from "anon";

revoke references on table "public"."service_pack" from "anon";

revoke select on table "public"."service_pack" from "anon";

revoke trigger on table "public"."service_pack" from "anon";

revoke truncate on table "public"."service_pack" from "anon";

revoke update on table "public"."service_pack" from "anon";

revoke delete on table "public"."service_pack" from "authenticated";

revoke insert on table "public"."service_pack" from "authenticated";

revoke references on table "public"."service_pack" from "authenticated";

revoke select on table "public"."service_pack" from "authenticated";

revoke trigger on table "public"."service_pack" from "authenticated";

revoke truncate on table "public"."service_pack" from "authenticated";

revoke update on table "public"."service_pack" from "authenticated";

revoke delete on table "public"."service_pack" from "service_role";

revoke insert on table "public"."service_pack" from "service_role";

revoke references on table "public"."service_pack" from "service_role";

revoke select on table "public"."service_pack" from "service_role";

revoke trigger on table "public"."service_pack" from "service_role";

revoke truncate on table "public"."service_pack" from "service_role";

revoke update on table "public"."service_pack" from "service_role";

revoke delete on table "public"."service_plan" from "anon";

revoke insert on table "public"."service_plan" from "anon";

revoke references on table "public"."service_plan" from "anon";

revoke select on table "public"."service_plan" from "anon";

revoke trigger on table "public"."service_plan" from "anon";

revoke truncate on table "public"."service_plan" from "anon";

revoke update on table "public"."service_plan" from "anon";

revoke delete on table "public"."service_plan" from "authenticated";

revoke insert on table "public"."service_plan" from "authenticated";

revoke references on table "public"."service_plan" from "authenticated";

revoke select on table "public"."service_plan" from "authenticated";

revoke trigger on table "public"."service_plan" from "authenticated";

revoke truncate on table "public"."service_plan" from "authenticated";

revoke update on table "public"."service_plan" from "authenticated";

revoke delete on table "public"."service_plan" from "service_role";

revoke insert on table "public"."service_plan" from "service_role";

revoke references on table "public"."service_plan" from "service_role";

revoke select on table "public"."service_plan" from "service_role";

revoke trigger on table "public"."service_plan" from "service_role";

revoke truncate on table "public"."service_plan" from "service_role";

revoke update on table "public"."service_plan" from "service_role";

revoke delete on table "public"."service_student" from "anon";

revoke insert on table "public"."service_student" from "anon";

revoke references on table "public"."service_student" from "anon";

revoke select on table "public"."service_student" from "anon";

revoke trigger on table "public"."service_student" from "anon";

revoke truncate on table "public"."service_student" from "anon";

revoke update on table "public"."service_student" from "anon";

revoke delete on table "public"."service_student" from "authenticated";

revoke insert on table "public"."service_student" from "authenticated";

revoke references on table "public"."service_student" from "authenticated";

revoke select on table "public"."service_student" from "authenticated";

revoke trigger on table "public"."service_student" from "authenticated";

revoke truncate on table "public"."service_student" from "authenticated";

revoke update on table "public"."service_student" from "authenticated";

revoke delete on table "public"."service_student" from "service_role";

revoke insert on table "public"."service_student" from "service_role";

revoke references on table "public"."service_student" from "service_role";

revoke select on table "public"."service_student" from "service_role";

revoke trigger on table "public"."service_student" from "service_role";

revoke truncate on table "public"."service_student" from "service_role";

revoke update on table "public"."service_student" from "service_role";

revoke delete on table "public"."sport" from "anon";

revoke insert on table "public"."sport" from "anon";

revoke references on table "public"."sport" from "anon";

revoke select on table "public"."sport" from "anon";

revoke trigger on table "public"."sport" from "anon";

revoke truncate on table "public"."sport" from "anon";

revoke update on table "public"."sport" from "anon";

revoke delete on table "public"."sport" from "authenticated";

revoke insert on table "public"."sport" from "authenticated";

revoke references on table "public"."sport" from "authenticated";

revoke select on table "public"."sport" from "authenticated";

revoke trigger on table "public"."sport" from "authenticated";

revoke truncate on table "public"."sport" from "authenticated";

revoke update on table "public"."sport" from "authenticated";

revoke delete on table "public"."sport" from "service_role";

revoke insert on table "public"."sport" from "service_role";

revoke references on table "public"."sport" from "service_role";

revoke select on table "public"."sport" from "service_role";

revoke trigger on table "public"."sport" from "service_role";

revoke truncate on table "public"."sport" from "service_role";

revoke update on table "public"."sport" from "service_role";

revoke delete on table "public"."sport_profile" from "anon";

revoke insert on table "public"."sport_profile" from "anon";

revoke references on table "public"."sport_profile" from "anon";

revoke select on table "public"."sport_profile" from "anon";

revoke trigger on table "public"."sport_profile" from "anon";

revoke truncate on table "public"."sport_profile" from "anon";

revoke update on table "public"."sport_profile" from "anon";

revoke delete on table "public"."sport_profile" from "authenticated";

revoke insert on table "public"."sport_profile" from "authenticated";

revoke references on table "public"."sport_profile" from "authenticated";

revoke select on table "public"."sport_profile" from "authenticated";

revoke trigger on table "public"."sport_profile" from "authenticated";

revoke truncate on table "public"."sport_profile" from "authenticated";

revoke update on table "public"."sport_profile" from "authenticated";

revoke delete on table "public"."sport_profile" from "service_role";

revoke insert on table "public"."sport_profile" from "service_role";

revoke references on table "public"."sport_profile" from "service_role";

revoke select on table "public"."sport_profile" from "service_role";

revoke trigger on table "public"."sport_profile" from "service_role";

revoke truncate on table "public"."sport_profile" from "service_role";

revoke update on table "public"."sport_profile" from "service_role";

revoke delete on table "public"."sport_school" from "anon";

revoke insert on table "public"."sport_school" from "anon";

revoke references on table "public"."sport_school" from "anon";

revoke select on table "public"."sport_school" from "anon";

revoke trigger on table "public"."sport_school" from "anon";

revoke truncate on table "public"."sport_school" from "anon";

revoke update on table "public"."sport_school" from "anon";

revoke delete on table "public"."sport_school" from "authenticated";

revoke insert on table "public"."sport_school" from "authenticated";

revoke references on table "public"."sport_school" from "authenticated";

revoke select on table "public"."sport_school" from "authenticated";

revoke trigger on table "public"."sport_school" from "authenticated";

revoke truncate on table "public"."sport_school" from "authenticated";

revoke update on table "public"."sport_school" from "authenticated";

revoke delete on table "public"."sport_school" from "service_role";

revoke insert on table "public"."sport_school" from "service_role";

revoke references on table "public"."sport_school" from "service_role";

revoke select on table "public"."sport_school" from "service_role";

revoke trigger on table "public"."sport_school" from "service_role";

revoke truncate on table "public"."sport_school" from "service_role";

revoke update on table "public"."sport_school" from "service_role";

revoke delete on table "public"."user_pwa" from "anon";

revoke insert on table "public"."user_pwa" from "anon";

revoke references on table "public"."user_pwa" from "anon";

revoke select on table "public"."user_pwa" from "anon";

revoke trigger on table "public"."user_pwa" from "anon";

revoke truncate on table "public"."user_pwa" from "anon";

revoke update on table "public"."user_pwa" from "anon";

revoke delete on table "public"."user_pwa" from "authenticated";

revoke insert on table "public"."user_pwa" from "authenticated";

revoke references on table "public"."user_pwa" from "authenticated";

revoke select on table "public"."user_pwa" from "authenticated";

revoke trigger on table "public"."user_pwa" from "authenticated";

revoke truncate on table "public"."user_pwa" from "authenticated";

revoke update on table "public"."user_pwa" from "authenticated";

revoke delete on table "public"."user_pwa" from "service_role";

revoke insert on table "public"."user_pwa" from "service_role";

revoke references on table "public"."user_pwa" from "service_role";

revoke select on table "public"."user_pwa" from "service_role";

revoke trigger on table "public"."user_pwa" from "service_role";

revoke truncate on table "public"."user_pwa" from "service_role";

revoke update on table "public"."user_pwa" from "service_role";

alter table "public"."attendance" drop constraint "attendance_scheduleId_fkey";

alter table "public"."attendance" drop constraint "attendance_serviceId_fkey";

alter table "public"."attendance" drop constraint "attendance_userId_fkey";

alter table "public"."base_rank" drop constraint "base_rank_sportId_fkey";

alter table "public"."booking" drop constraint "booking_leadId_fkey";

alter table "public"."booking" drop constraint "booking_packProfileId_fkey";

alter table "public"."booking" drop constraint "booking_serviceId_fkey";

alter table "public"."booking" drop constraint "booking_teacherScheduleId_fkey";

alter table "public"."cancelation_policy" drop constraint "cancelation_policy_packId_fkey";

alter table "public"."cancelation_policy" drop constraint "unique_hour_serviceid";

alter table "public"."category" drop constraint "unique_name_schoolid";

alter table "public"."contract" drop constraint "contract_planId_fkey";

alter table "public"."contract" drop constraint "contract_schoolid_fkey";

alter table "public"."contract" drop constraint "contract_studentId_fkey";

alter table "public"."contract" drop constraint "contract_subscriptionid_fkey";

alter table "public"."debt" drop constraint "debt_debtRootId_fkey";

alter table "public"."debt_category" drop constraint "debt_category_debtRootId_fkey";

alter table "public"."debt_category" drop constraint "debt_category_schoolId_fkey1";

alter table "public"."debt_root" drop constraint "debt_root_schoolId_fkey";

alter table "public"."face" drop constraint "face_email_key";

alter table "public"."lead" drop constraint "lead_schoolId_fkey";

alter table "public"."pack" drop constraint "pkg_schoolId_fkey";

alter table "public"."pack_profile" drop constraint "pack_profile_packId_fkey";

alter table "public"."pack_profile" drop constraint "pack_profile_paymentId_fkey";

alter table "public"."pack_profile" drop constraint "pack_profile_saleId_fkey";

alter table "public"."pack_profile" drop constraint "pkg_profile_profileId_fkey";

alter table "public"."payment" drop constraint "payment_packId_fkey";

alter table "public"."payment" drop constraint "payment_planId_fkey";

alter table "public"."payment" drop constraint "payment_profileAsId_fkey";

alter table "public"."profile" drop constraint "profile_asId_key";

alter table "public"."profile" drop constraint "profile_email_type";

alter table "public"."profile" drop constraint "profile_schoolId_fkey";

alter table "public"."rank" drop constraint "rank_baseRankId_fkey";

alter table "public"."rank" drop constraint "rank_nextRank_fkey";

alter table "public"."rank" drop constraint "rank_sportId_fkey";

alter table "public"."rank_point" drop constraint "rank_point_sportId_fkey";

alter table "public"."rank_point" drop constraint "rank_point_userId_fkey";

alter table "public"."rank_school" drop constraint "rank_school_rankId_fkey";

alter table "public"."rank_school" drop constraint "rank_school_schoolId_fkey";

alter table "public"."rank_school" drop constraint "unique_school_rank";

alter table "public"."rank_student" drop constraint "rank_student_profileId_fkey";

alter table "public"."rank_student" drop constraint "rank_student_rankId_fkey";

alter table "public"."rank_student" drop constraint "rank_student_sportId_fkey";

alter table "public"."sale" drop constraint "sale_clientId_fkey";

alter table "public"."sale" drop constraint "sale_packId_fkey";

alter table "public"."sale" drop constraint "sale_saleRootId_fkey";

alter table "public"."sale" drop constraint "sale_schoolId_fkey";

alter table "public"."sale_root" drop constraint "sale_root_clientId_fkey";

alter table "public"."sale_root" drop constraint "sale_root_packId_fkey";

alter table "public"."sale_root" drop constraint "sale_root_schoolId_fkey";

alter table "public"."schedule" drop constraint "unique_service_hour_number_active";

alter table "public"."school_profile" drop constraint "school_profile_profileId_fkey";

alter table "public"."school_profile" drop constraint "school_profile_schoolId_fkey";

alter table "public"."school_settings" drop constraint "school_settings_schoolId_fkey";

alter table "public"."school_settings" drop constraint "school_settings_school_id_key";

alter table "public"."service" drop constraint "service_schoolId_fkey";

alter table "public"."service" drop constraint "service_sportId_fkey";

alter table "public"."service_pack" drop constraint "pack_service_packId_fkey";

alter table "public"."service_pack" drop constraint "pack_service_serviceId_fkey";

alter table "public"."service_plan" drop constraint "plan_service_planId_fkey";

alter table "public"."service_plan" drop constraint "plan_service_serviceId_fkey";

alter table "public"."service_student" drop constraint "service_profile_profileId_fkey";

alter table "public"."service_student" drop constraint "service_profile_serviceId_fkey";

alter table "public"."sport_profile" drop constraint "sport_profile_profileId_fkey";

alter table "public"."sport_profile" drop constraint "sport_profile_sportId_fkey";

alter table "public"."sport_school" drop constraint "sport_school_schoolId_fkey";

alter table "public"."sport_school" drop constraint "sport_school_sportId_fkey";

alter table "public"."subscription" drop constraint "subscription_asaas_planId_fkey";

alter table "public"."subscription" drop constraint "subscription_asaas_profileId_fkey";

alter table "public"."user_pwa" drop constraint "user_pwa_profileId_fkey";

alter table "public"."user_pwa" drop constraint "user_pwa_schoolId_fkey";

alter table "public"."attendance" drop constraint "user_date";

alter table "public"."booking" drop constraint "booking_packageId_fkey";

alter table "public"."lesson" drop constraint "lesson_courseId_fkey";

alter table "public"."lesson" drop constraint "lesson_moduleId_fkey";

alter table "public"."module" drop constraint "module_courseId_fkey";

alter table "public"."plan" drop constraint "plan_schoolId_fkey";

drop function if exists "public"."generate_next_month_teacher_schedule"();

drop function if exists "public"."get_all_bookings"(school uuid);

drop function if exists "public"."get_available_schedules"("selectedDay" date, "selectedServiceId" uuid);

drop function if exists "public"."get_user_info"(userid uuid, sportid uuid);

drop function if exists "public"."insert_teacher_schedules"();

drop function if exists "public"."verify_pack_expiration"();

drop view if exists "public"."attendance_monthly";

drop view if exists "public"."attendance_yearly";

alter table "public"."base_rank" drop constraint "base_rank_pkey";

alter table "public"."contract" drop constraint "contract_pkey";

alter table "public"."debt_root" drop constraint "debt_root_pkey";

alter table "public"."lead" drop constraint "lead_pkey";

alter table "public"."pack" drop constraint "package_pkey";

alter table "public"."pack_profile" drop constraint "pkg_profile_pkey";

alter table "public"."payment" drop constraint "payment_pkey";

alter table "public"."rank" drop constraint "rank_pkey";

alter table "public"."rank_point" drop constraint "rank_point_pkey";

alter table "public"."rank_school" drop constraint "rank_school_pkey";

alter table "public"."rank_student" drop constraint "rank_student_pkey";

alter table "public"."sale" drop constraint "sale_pkey";

alter table "public"."sale_root" drop constraint "sale_root_pkey";

alter table "public"."school_profile" drop constraint "school_profile_pkey";

alter table "public"."school_settings" drop constraint "school_settings_pkey";

alter table "public"."service_pack" drop constraint "pack_service_pkey";

alter table "public"."service_plan" drop constraint "plan_service_pkey";

alter table "public"."service_student" drop constraint "service_profile_pkey";

alter table "public"."sport" drop constraint "sport_pkey";

alter table "public"."sport_profile" drop constraint "sport_profile_pkey";

alter table "public"."sport_school" drop constraint "sport_school_pkey";

alter table "public"."subscription" drop constraint "subscription_asaas_pkey";

alter table "public"."user_pwa" drop constraint "user_pwa_pkey";

drop index if exists "public"."base_rank_pkey";

drop index if exists "public"."contract_pkey";

drop index if exists "public"."debt_root_pkey";

drop index if exists "public"."face_email_key";

drop index if exists "public"."lead_pkey";

drop index if exists "public"."plan_service_pkey";

drop index if exists "public"."profile_asId_key";

drop index if exists "public"."profile_email_type";

drop index if exists "public"."rank_pkey";

drop index if exists "public"."rank_point_pkey";

drop index if exists "public"."rank_school_pkey";

drop index if exists "public"."rank_student_pkey";

drop index if exists "public"."sale_pkey";

drop index if exists "public"."sale_root_pkey";

drop index if exists "public"."school_profile_pkey";

drop index if exists "public"."school_settings_pkey";

drop index if exists "public"."school_settings_school_id_key";

drop index if exists "public"."service_profile_pkey";

drop index if exists "public"."sport_pkey";

drop index if exists "public"."sport_profile_pkey";

drop index if exists "public"."sport_school_pkey";

drop index if exists "public"."unique_hour_serviceid";

drop index if exists "public"."unique_name_schoolid";

drop index if exists "public"."unique_school_rank";

drop index if exists "public"."unique_service_hour_number_active";

drop index if exists "public"."user_pwa_pkey";

drop index if exists "public"."pack_service_pkey";

drop index if exists "public"."package_pkey";

drop index if exists "public"."payment_pkey";

drop index if exists "public"."pkg_profile_pkey";

drop index if exists "public"."plan_pkey";

drop index if exists "public"."subscription_asaas_pkey";

drop index if exists "public"."user_date";

drop table "public"."base_rank";

drop table "public"."contract";

drop table "public"."debt_root";

drop table "public"."lead";

drop table "public"."pack";

drop table "public"."pack_profile";

drop table "public"."payment";

drop table "public"."rank";

drop table "public"."rank_point";

drop table "public"."rank_school";

drop table "public"."rank_student";

drop table "public"."sale";

drop table "public"."sale_root";

drop table "public"."school_profile";

drop table "public"."school_settings";

drop table "public"."service_pack";

drop table "public"."service_plan";

drop table "public"."service_student";

drop table "public"."sport";

drop table "public"."sport_profile";

drop table "public"."sport_school";

drop table "public"."user_pwa";

alter table "public"."profile" alter column "type" drop default;

alter type "public"."app_role" rename to "app_role__old_version_to_be_dropped";

create type "public"."app_role" as enum ('admin', 'student');

alter type "public"."payment_method" rename to "payment_method__old_version_to_be_dropped";

create type "public"."payment_method" as enum ('credit_card', 'pix', 'boleto');

alter type "public"."user_type" rename to "user_type__old_version_to_be_dropped";

create type "public"."user_type" as enum ('admin', 'student', 'teacher');


  create table "public"."mp_application" (
    "id" character varying not null,
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null
      );


alter table "public"."mp_application" enable row level security;


  create table "public"."pack_service" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "packId" uuid not null,
    "serviceId" uuid not null
      );


alter table "public"."pack_service" enable row level security;


  create table "public"."payment_pkg" (
    "id" character varying not null,
    "createdAt" timestamp with time zone not null default now(),
    "status" character varying not null,
    "statusDetails" character varying,
    "captured" boolean,
    "collectorId" character varying,
    "dateApproved" timestamp with time zone,
    "dateCreated" timestamp with time zone,
    "profileId" uuid,
    "transactionAmount" numeric,
    "typeId" character varying,
    "dateLastUpdated" timestamp without time zone,
    "pkgId" uuid default gen_random_uuid(),
    "domain" character varying
      );


alter table "public"."payment_pkg" enable row level security;


  create table "public"."payment_pkg_notification" (
    "action" character varying not null,
    "date" timestamp without time zone,
    "type" character varying,
    "createdAt" timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
    "id" character varying not null,
    "domain" character varying,
    "profileId" uuid
      );


alter table "public"."payment_pkg_notification" enable row level security;


  create table "public"."payment_plan" (
    "id" character varying not null,
    "createdAt" timestamp with time zone not null default now(),
    "status" character varying not null,
    "statusDetails" character varying,
    "captured" boolean,
    "collectorId" character varying,
    "dateApproved" timestamp with time zone,
    "dateCreated" timestamp with time zone,
    "profileId" uuid,
    "transactionAmount" numeric,
    "typeId" character varying,
    "planId" character varying,
    "dateLastUpdated" timestamp without time zone,
    "subscriptionId" character varying,
    "domain" character varying
      );


alter table "public"."payment_plan" enable row level security;


  create table "public"."payment_plan_notification" (
    "action" character varying not null,
    "date" timestamp without time zone,
    "type" character varying,
    "createdAt" timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
    "id" character varying not null,
    "profileId" uuid,
    "domain" character varying
      );


alter table "public"."payment_plan_notification" enable row level security;


  create table "public"."pkg" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null,
    "schoolId" uuid not null default gen_random_uuid(),
    "price" numeric not null,
    "active" boolean not null default true,
    "expires" smallint,
    "use" smallint
      );


alter table "public"."pkg" enable row level security;


  create table "public"."pkg_profile" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "pkgId" uuid not null default gen_random_uuid(),
    "profileId" uuid not null default gen_random_uuid(),
    "active" boolean not null default true
      );


alter table "public"."pkg_profile" enable row level security;


  create table "public"."plan_profile" (
    "createdAt" timestamp with time zone not null default now(),
    "planId" character varying not null,
    "profileId" uuid not null default gen_random_uuid(),
    "active" boolean not null default true,
    "id" uuid not null default gen_random_uuid()
      );


alter table "public"."plan_profile" enable row level security;


  create table "public"."subscription_asaas" (
    "id" character varying not null,
    "createdAt" date not null default now(),
    "object" character varying,
    "customer" character varying not null,
    "paymentLink" character varying,
    "billingType" billing_type,
    "cycle" cycle not null,
    "value" numeric not null,
    "nextDueDate" date,
    "endDate" date,
    "description" character varying,
    "fineValue" numeric,
    "interestValue" numeric,
    "deleted" boolean,
    "maxPayments" smallint,
    "externalReference" character varying,
    "profileId" uuid,
    "status" status not null default 'ACTIVE'::status
      );


alter table "public"."subscription_asaas" enable row level security;


  create table "public"."transaction_detail" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "paymentMethodReferenceId" character varying,
    "acquirerReference" character varying,
    "netReceivedAmount" numeric,
    "totalPaidAmount" numeric,
    "overPaidAmount" numeric,
    "installmentAmount" smallint,
    "financialInstitution" character varying,
    "transactionId" character varying,
    "digitableLine" character varying,
    "verificationCode" character varying,
    "bankTransferId" character varying,
    "barCode" character varying
      );


alter table "public"."transaction_detail" enable row level security;


  create table "public"."user_roles" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "role" app_role not null,
    "createdAt" timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text)
      );


alter table "public"."user_roles" enable row level security;

alter table "public"."debt" alter column method type "public"."payment_method" using method::text::"public"."payment_method";

alter table "public"."profile" alter column type type "public"."user_type" using type::text::"public"."user_type";

alter table "public"."profile" alter column "type" set default 'student'::user_type;

drop type "public"."app_role__old_version_to_be_dropped";

drop type "public"."payment_method__old_version_to_be_dropped";

drop type "public"."user_type__old_version_to_be_dropped";

alter table "public"."attendance" drop column "confirmed";

alter table "public"."booking" drop column "leadId";

alter table "public"."booking" drop column "packId";

alter table "public"."booking" drop column "packProfileId";

alter table "public"."booking" drop column "teacherScheduleId";

alter table "public"."booking" add column "pkgId" uuid default gen_random_uuid();

alter table "public"."booking" alter column "userId" set not null;

alter table "public"."cancelation_policy" drop column "packId";

alter table "public"."cancelation_policy" alter column "hour" set data type text using "hour"::text;

alter table "public"."cancelation_policy" alter column "percentage" drop default;

alter table "public"."course" add column "featureVideo" uuid not null;

alter table "public"."course" alter column "description" set not null;

alter table "public"."course" alter column "featureImg" set not null;

alter table "public"."course" alter column "instructorId" set default gen_random_uuid();

alter table "public"."course" alter column "instructorId" set not null;

alter table "public"."course" alter column "schoolId" set not null;

alter table "public"."debt" drop column "debtRootId";

alter table "public"."debt" add column "isFixed" boolean default false;

alter table "public"."debt" add column "repetitions" smallint;

alter table "public"."debt" alter column "billingDate" set not null;

alter table "public"."debt" alter column "billingDate" set data type timestamp with time zone using "billingDate"::timestamp with time zone;

alter table "public"."debt_category" drop column "debtRootId";

alter table "public"."debt_category" drop column "schoolId";

alter table "public"."debt_category" add column "debtId" uuid;

alter table "public"."plan" drop column "billingDay";

alter table "public"."plan" drop column "display";

alter table "public"."plan" drop column "fine";

alter table "public"."plan" drop column "fineType";

alter table "public"."plan" drop column "interest";

alter table "public"."plan" alter column "active" set default false;

alter table "public"."plan" alter column "id" drop default;

alter table "public"."plan" alter column "id" set data type character varying using "id"::character varying;

alter table "public"."profile" drop column "asId";

alter table "public"."profile" drop column "status";

alter table "public"."profile" add column "mpId" character varying;

alter table "public"."profile" alter column "active" set default true;

alter table "public"."profile" alter column "faceImage" set data type character varying using "faceImage"::character varying;

alter table "public"."school" drop column "cnpj";

alter table "public"."service" drop column "duration";

alter table "public"."service" drop column "policy";

alter table "public"."service" drop column "randomTeacher";

alter table "public"."service" drop column "sportId";

alter table "public"."service" add column "cancelationPolicy" text not null;

alter table "public"."subscription" drop column "billingType";

alter table "public"."subscription" drop column "cardBrand";

alter table "public"."subscription" drop column "cardNumber";

alter table "public"."subscription" drop column "customer";

alter table "public"."subscription" drop column "cycle";

alter table "public"."subscription" drop column "deleted";

alter table "public"."subscription" drop column "endDate";

alter table "public"."subscription" drop column "fineValue";

alter table "public"."subscription" drop column "interestValue";

alter table "public"."subscription" drop column "maxPayments";

alter table "public"."subscription" drop column "nextDueDate";

alter table "public"."subscription" drop column "object";

alter table "public"."subscription" drop column "paymentLink";

alter table "public"."subscription" drop column "paymentStatus";

alter table "public"."subscription" drop column "profileId";

alter table "public"."subscription" drop column "value";

alter table "public"."subscription" add column "active" boolean not null default false;

alter table "public"."subscription" add column "applicationId" character varying;

alter table "public"."subscription" add column "backUrl" text;

alter table "public"."subscription" add column "billingDayProportional" boolean;

alter table "public"."subscription" add column "cardId" character varying;

alter table "public"."subscription" add column "chargedAmount" numeric;

alter table "public"."subscription" add column "chargedQty" smallint;

alter table "public"."subscription" add column "collectorId" character varying;

alter table "public"."subscription" add column "endsAt" date;

alter table "public"."subscription" add column "externalReference" character varying;

alter table "public"."subscription" add column "frequency" integer;

alter table "public"."subscription" add column "frequencyType" text;

alter table "public"."subscription" add column "hasbillingdata" boolean;

alter table "public"."subscription" add column "initPoint" text;

alter table "public"."subscription" add column "invited" boolean default false;

alter table "public"."subscription" add column "lastChargedAmount" numeric;

alter table "public"."subscription" add column "lastChargedDate" timestamp with time zone;

alter table "public"."subscription" add column "name" text not null;

alter table "public"."subscription" add column "nextPaymentDate" date;

alter table "public"."subscription" add column "payerId" character varying;

alter table "public"."subscription" add column "paymentMethodId" character varying;

alter table "public"."subscription" add column "pendingChargeAmount" numeric;

alter table "public"."subscription" add column "pendingChargeQty" smallint;

alter table "public"."subscription" add column "reason" text;

alter table "public"."subscription" add column "semaphore" semaphore;

alter table "public"."subscription" add column "startDate" date;

alter table "public"."subscription" add column "transactionAmount" integer;

alter table "public"."subscription" add column "type" subscription_type not null default 'payed'::subscription_type;

alter table "public"."subscription" add column "userId" uuid not null default auth.uid();

alter table "public"."subscription" alter column "billingDay" set data type integer using "billingDay"::integer;

alter table "public"."subscription" alter column "createdAt" set default (now() AT TIME ZONE 'utc'::text);

alter table "public"."subscription" alter column "createdAt" set data type timestamp with time zone using "createdAt"::timestamp with time zone;

alter table "public"."subscription" alter column "description" set data type text using "description"::text;

alter table "public"."subscription" alter column "planId" set data type character varying using "planId"::character varying;

alter table "public"."subscription" alter column "status" drop default;

alter table "public"."subscription" alter column "status" set data type subscription_status using "status"::text::subscription_status;

alter table "public"."video" alter column "duration" set not null;

alter table "public"."video" alter column "name" set not null;

alter table "public"."video" alter column "publicPlaybackId" set not null;

alter table "public"."video" alter column "schoolId" set not null;

alter table "public"."video" alter column "videoId" set not null;

drop type "public"."fine_type";

drop type "public"."pack_profile_status";

drop type "public"."payment_status";

drop type "public"."sports";

drop type "public"."status_type";

CREATE UNIQUE INDEX booking_service_user ON public.booking USING btree (day, "scheduleId", "serviceId", "userId");

CREATE UNIQUE INDEX cancelation_policy_hour_key ON public.cancelation_policy USING btree (hour);

CREATE UNIQUE INDEX cancelation_policy_percentage_key ON public.cancelation_policy USING btree (percentage);

CREATE UNIQUE INDEX email_unique ON public.face USING btree (email);

CREATE UNIQUE INDEX mp_application_name_key ON public.mp_application USING btree (name);

CREATE UNIQUE INDEX mp_application_pkey ON public.mp_application USING btree (id);

CREATE UNIQUE INDEX payment_notification_pkey ON public.payment_plan_notification USING btree (id);

CREATE UNIQUE INDEX payment_pkg_notification_pkey ON public.payment_pkg_notification USING btree (id);

CREATE UNIQUE INDEX payment_pkg_pkey ON public.payment_pkg USING btree (id);

CREATE UNIQUE INDEX plan_profile_pkey ON public.plan_profile USING btree (id);

CREATE INDEX subscription_id_idx ON public.subscription USING btree (id);

CREATE UNIQUE INDEX subscription_pkey ON public.subscription USING btree (id);

CREATE INDEX "subscription_userId_idx" ON public.subscription USING btree ("userId");

CREATE UNIQUE INDEX transaction_detail_pkey ON public.transaction_detail USING btree (id);

CREATE UNIQUE INDEX user_roles_pkey ON public.user_roles USING btree (id);

CREATE UNIQUE INDEX user_roles_user_id_role_key ON public.user_roles USING btree (user_id, role);

CREATE UNIQUE INDEX pack_service_pkey ON public.pack_service USING btree (id);

CREATE UNIQUE INDEX package_pkey ON public.pkg USING btree (id);

CREATE UNIQUE INDEX payment_pkey ON public.payment_plan USING btree (id);

CREATE UNIQUE INDEX pkg_profile_pkey ON public.pkg_profile USING btree (id);

CREATE UNIQUE INDEX plan_pkey ON public.plan USING btree (id);

CREATE UNIQUE INDEX subscription_asaas_pkey ON public.subscription_asaas USING btree (id);

CREATE UNIQUE INDEX user_date ON public.attendance USING btree ("userId", date);

alter table "public"."mp_application" add constraint "mp_application_pkey" PRIMARY KEY using index "mp_application_pkey";

alter table "public"."pack_service" add constraint "pack_service_pkey" PRIMARY KEY using index "pack_service_pkey";

alter table "public"."payment_pkg" add constraint "payment_pkg_pkey" PRIMARY KEY using index "payment_pkg_pkey";

alter table "public"."payment_pkg_notification" add constraint "payment_pkg_notification_pkey" PRIMARY KEY using index "payment_pkg_notification_pkey";

alter table "public"."payment_plan" add constraint "payment_pkey" PRIMARY KEY using index "payment_pkey";

alter table "public"."payment_plan_notification" add constraint "payment_notification_pkey" PRIMARY KEY using index "payment_notification_pkey";

alter table "public"."pkg" add constraint "package_pkey" PRIMARY KEY using index "package_pkey";

alter table "public"."pkg_profile" add constraint "pkg_profile_pkey" PRIMARY KEY using index "pkg_profile_pkey";

alter table "public"."plan_profile" add constraint "plan_profile_pkey" PRIMARY KEY using index "plan_profile_pkey";

alter table "public"."subscription" add constraint "subscription_pkey" PRIMARY KEY using index "subscription_pkey";

alter table "public"."subscription_asaas" add constraint "subscription_asaas_pkey" PRIMARY KEY using index "subscription_asaas_pkey";

alter table "public"."transaction_detail" add constraint "transaction_detail_pkey" PRIMARY KEY using index "transaction_detail_pkey";

alter table "public"."user_roles" add constraint "user_roles_pkey" PRIMARY KEY using index "user_roles_pkey";

alter table "public"."attendance" add constraint "attendance_user_fk" FOREIGN KEY ("userId") REFERENCES profile(id) not valid;

alter table "public"."attendance" validate constraint "attendance_user_fk";

alter table "public"."booking" add constraint "booking_service_fk" FOREIGN KEY ("serviceId") REFERENCES service(id) not valid;

alter table "public"."booking" validate constraint "booking_service_fk";

alter table "public"."booking" add constraint "booking_service_user" UNIQUE using index "booking_service_user";

alter table "public"."cancelation_policy" add constraint "cancelation_policy_hour_key" UNIQUE using index "cancelation_policy_hour_key";

alter table "public"."cancelation_policy" add constraint "cancelation_policy_percentage_key" UNIQUE using index "cancelation_policy_percentage_key";

alter table "public"."course" add constraint "course_featureVideo_fkey" FOREIGN KEY ("featureVideo") REFERENCES video(id) not valid;

alter table "public"."course" validate constraint "course_featureVideo_fkey";

alter table "public"."debt_category" add constraint "debt_category_debtId_fkey" FOREIGN KEY ("debtId") REFERENCES debt(id) not valid;

alter table "public"."debt_category" validate constraint "debt_category_debtId_fkey";

alter table "public"."face" add constraint "email_unique" UNIQUE using index "email_unique";

alter table "public"."mp_application" add constraint "mp_application_name_key" UNIQUE using index "mp_application_name_key";

alter table "public"."pack_service" add constraint "pack_service_packId_fkey" FOREIGN KEY ("packId") REFERENCES pkg(id) not valid;

alter table "public"."pack_service" validate constraint "pack_service_packId_fkey";

alter table "public"."pack_service" add constraint "pack_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) not valid;

alter table "public"."pack_service" validate constraint "pack_service_serviceId_fkey";

alter table "public"."payment_pkg" add constraint "payment_pkg_pkg_fkey" FOREIGN KEY ("pkgId") REFERENCES pkg(id) not valid;

alter table "public"."payment_pkg" validate constraint "payment_pkg_pkg_fkey";

alter table "public"."payment_pkg" add constraint "payment_pkg_profileid_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment_pkg" validate constraint "payment_pkg_profileid_fkey";

alter table "public"."payment_pkg_notification" add constraint "payment_pkg_notification_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."payment_pkg_notification" validate constraint "payment_pkg_notification_profileId_fkey";

alter table "public"."payment_plan" add constraint "payment_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."payment_plan" validate constraint "payment_planId_fkey";

alter table "public"."payment_plan" add constraint "payment_plan_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES subscription(id) not valid;

alter table "public"."payment_plan" validate constraint "payment_plan_subscriptionId_fkey";

alter table "public"."payment_plan" add constraint "payment_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment_plan" validate constraint "payment_profileId_fkey";

alter table "public"."payment_plan_notification" add constraint "payment_plan_notification_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment_plan_notification" validate constraint "payment_plan_notification_profileId_fkey";

alter table "public"."pkg" add constraint "package_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."pkg" validate constraint "package_schoolId_fkey";

alter table "public"."pkg_profile" add constraint "pkg_profile_pkgId_fkey" FOREIGN KEY ("pkgId") REFERENCES pkg(id) not valid;

alter table "public"."pkg_profile" validate constraint "pkg_profile_pkgId_fkey";

alter table "public"."pkg_profile" add constraint "pkg_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."pkg_profile" validate constraint "pkg_profile_profileId_fkey";

alter table "public"."plan_profile" add constraint "plan_profile_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."plan_profile" validate constraint "plan_profile_planId_fkey";

alter table "public"."plan_profile" add constraint "plan_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."plan_profile" validate constraint "plan_profile_profileId_fkey";

alter table "public"."profile" add constraint "user_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."profile" validate constraint "user_schoolId_fkey";

alter table "public"."schedule" add constraint "schedule_hour_check" CHECK ((char_length(hour) = 5)) not valid;

alter table "public"."schedule" validate constraint "schedule_hour_check";

alter table "public"."service" add constraint "service_school_fk" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."service" validate constraint "service_school_fk";

alter table "public"."subscription" add constraint "subscription_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES mp_application(id) not valid;

alter table "public"."subscription" validate constraint "subscription_applicationId_fkey";

alter table "public"."subscription" add constraint "subscription_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."subscription" validate constraint "subscription_planId_fkey";

alter table "public"."subscription" add constraint "subscription_useId_fk" FOREIGN KEY ("userId") REFERENCES profile(id) not valid;

alter table "public"."subscription" validate constraint "subscription_useId_fk";

alter table "public"."subscription_asaas" add constraint "subscription_asaas_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_asaas" validate constraint "subscription_asaas_profileId_fkey";

alter table "public"."user_roles" add constraint "user_roles_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."user_roles" validate constraint "user_roles_user_id_fkey";

alter table "public"."user_roles" add constraint "user_roles_user_id_role_key" UNIQUE using index "user_roles_user_id_role_key";

alter table "public"."attendance" add constraint "user_date" UNIQUE using index "user_date";

alter table "public"."booking" add constraint "booking_packageId_fkey" FOREIGN KEY ("pkgId") REFERENCES pkg(id) not valid;

alter table "public"."booking" validate constraint "booking_packageId_fkey";

alter table "public"."lesson" add constraint "lesson_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES course(id) not valid;

alter table "public"."lesson" validate constraint "lesson_courseId_fkey";

alter table "public"."lesson" add constraint "lesson_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES module(id) not valid;

alter table "public"."lesson" validate constraint "lesson_moduleId_fkey";

alter table "public"."module" add constraint "module_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES course(id) not valid;

alter table "public"."module" validate constraint "module_courseId_fkey";

alter table "public"."plan" add constraint "plan_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."plan" validate constraint "plan_schoolId_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
AS $function$
  declare
    claims jsonb;
    user_role public.app_role;
  begin
    select role into user_role from public.user_roles where user_id = (event->>'user_id')::uuid;

    claims := event->'claims';

    if user_role is not null then
      claims := jsonb_set(claims, '{user_role}', to_jsonb(user_role));
    else
      claims := jsonb_set(claims, '{user_role}', 'null');
    end if;

    event := jsonb_set(event, '{claims}', claims);

    return event;
  end;
$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_pkg()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_pkg_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg_notification?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan_notification?paymentId=%s&profileId=%s&domain=%s',
            NEW.id,
            NEW."profileId",
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_subscription()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response json;
BEGIN
    -- Make the HTTP request to the specified URL with the Bearer token
    SELECT * INTO response
    FROM http_post(
        'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_subscription',
        json_build_object('data', NEW.*)::text,
        'Authorization=Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'
    );

    RETURN NEW;
END;$function$
;

CREATE OR REPLACE FUNCTION public.verify_pkg_expiration()
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
  expired_profile RECORD;
BEGIN
  FOR expired_profile IN
    SELECT *
    FROM public.pkg_profile pp
    JOIN public.pkg p ON pp."pkgId" = p.id
    WHERE (pp."createdAt" + INTERVAL '1 day' * p.expires) < NOW()
  LOOP
    UPDATE public.pkg_profile
    SET active = false
    WHERE id = expired_profile.id;
  END LOOP;
END;$function$
;

create or replace view "public"."attendance_monthly" as  SELECT a."userId",
    a."serviceId",
    a."scheduleId",
    u.name,
    EXTRACT(year FROM a.date) AS year,
    EXTRACT(month FROM a.date) AS month,
    count(*) AS total
   FROM (attendance a
     JOIN profile u ON ((a."userId" = u.id)))
  GROUP BY a."userId", a."serviceId", a."scheduleId", u.name, (EXTRACT(year FROM a.date)), (EXTRACT(month FROM a.date))
  ORDER BY (count(*)) DESC;


create or replace view "public"."attendance_yearly" as  SELECT a."userId",
    a."serviceId",
    a."scheduleId",
    u.name,
    EXTRACT(year FROM a.date) AS year,
    count(*) AS total
   FROM (attendance a
     JOIN profile u ON ((a."userId" = u.id)))
  GROUP BY a."userId", a."serviceId", a."scheduleId", u.name, (EXTRACT(year FROM a.date))
  ORDER BY (count(*)) DESC;


CREATE OR REPLACE FUNCTION public.mark_expired_bookings()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
  expired_booking RECORD;
BEGIN
  FOR expired_booking IN
    SELECT *
    FROM public.booking b
    JOIN public.schedule s ON b."scheduleId" = s.id
    WHERE b.day + s.hour::INTERVAL < NOW()
  LOOP
    UPDATE public.booking
    SET status = 'expired'
    WHERE id = expired_booking.id;
  END LOOP;
END;
$function$
;

grant delete on table "public"."mp_application" to "anon";

grant insert on table "public"."mp_application" to "anon";

grant references on table "public"."mp_application" to "anon";

grant select on table "public"."mp_application" to "anon";

grant trigger on table "public"."mp_application" to "anon";

grant truncate on table "public"."mp_application" to "anon";

grant update on table "public"."mp_application" to "anon";

grant delete on table "public"."mp_application" to "authenticated";

grant insert on table "public"."mp_application" to "authenticated";

grant references on table "public"."mp_application" to "authenticated";

grant select on table "public"."mp_application" to "authenticated";

grant trigger on table "public"."mp_application" to "authenticated";

grant truncate on table "public"."mp_application" to "authenticated";

grant update on table "public"."mp_application" to "authenticated";

grant delete on table "public"."mp_application" to "service_role";

grant insert on table "public"."mp_application" to "service_role";

grant references on table "public"."mp_application" to "service_role";

grant select on table "public"."mp_application" to "service_role";

grant trigger on table "public"."mp_application" to "service_role";

grant truncate on table "public"."mp_application" to "service_role";

grant update on table "public"."mp_application" to "service_role";

grant delete on table "public"."pack_service" to "anon";

grant insert on table "public"."pack_service" to "anon";

grant references on table "public"."pack_service" to "anon";

grant select on table "public"."pack_service" to "anon";

grant trigger on table "public"."pack_service" to "anon";

grant truncate on table "public"."pack_service" to "anon";

grant update on table "public"."pack_service" to "anon";

grant delete on table "public"."pack_service" to "authenticated";

grant insert on table "public"."pack_service" to "authenticated";

grant references on table "public"."pack_service" to "authenticated";

grant select on table "public"."pack_service" to "authenticated";

grant trigger on table "public"."pack_service" to "authenticated";

grant truncate on table "public"."pack_service" to "authenticated";

grant update on table "public"."pack_service" to "authenticated";

grant delete on table "public"."pack_service" to "service_role";

grant insert on table "public"."pack_service" to "service_role";

grant references on table "public"."pack_service" to "service_role";

grant select on table "public"."pack_service" to "service_role";

grant trigger on table "public"."pack_service" to "service_role";

grant truncate on table "public"."pack_service" to "service_role";

grant update on table "public"."pack_service" to "service_role";

grant delete on table "public"."payment_pkg" to "anon";

grant insert on table "public"."payment_pkg" to "anon";

grant references on table "public"."payment_pkg" to "anon";

grant select on table "public"."payment_pkg" to "anon";

grant trigger on table "public"."payment_pkg" to "anon";

grant truncate on table "public"."payment_pkg" to "anon";

grant update on table "public"."payment_pkg" to "anon";

grant delete on table "public"."payment_pkg" to "authenticated";

grant insert on table "public"."payment_pkg" to "authenticated";

grant references on table "public"."payment_pkg" to "authenticated";

grant select on table "public"."payment_pkg" to "authenticated";

grant trigger on table "public"."payment_pkg" to "authenticated";

grant truncate on table "public"."payment_pkg" to "authenticated";

grant update on table "public"."payment_pkg" to "authenticated";

grant delete on table "public"."payment_pkg" to "service_role";

grant insert on table "public"."payment_pkg" to "service_role";

grant references on table "public"."payment_pkg" to "service_role";

grant select on table "public"."payment_pkg" to "service_role";

grant trigger on table "public"."payment_pkg" to "service_role";

grant truncate on table "public"."payment_pkg" to "service_role";

grant update on table "public"."payment_pkg" to "service_role";

grant delete on table "public"."payment_pkg_notification" to "anon";

grant insert on table "public"."payment_pkg_notification" to "anon";

grant references on table "public"."payment_pkg_notification" to "anon";

grant select on table "public"."payment_pkg_notification" to "anon";

grant trigger on table "public"."payment_pkg_notification" to "anon";

grant truncate on table "public"."payment_pkg_notification" to "anon";

grant update on table "public"."payment_pkg_notification" to "anon";

grant delete on table "public"."payment_pkg_notification" to "authenticated";

grant insert on table "public"."payment_pkg_notification" to "authenticated";

grant references on table "public"."payment_pkg_notification" to "authenticated";

grant select on table "public"."payment_pkg_notification" to "authenticated";

grant trigger on table "public"."payment_pkg_notification" to "authenticated";

grant truncate on table "public"."payment_pkg_notification" to "authenticated";

grant update on table "public"."payment_pkg_notification" to "authenticated";

grant delete on table "public"."payment_pkg_notification" to "service_role";

grant insert on table "public"."payment_pkg_notification" to "service_role";

grant references on table "public"."payment_pkg_notification" to "service_role";

grant select on table "public"."payment_pkg_notification" to "service_role";

grant trigger on table "public"."payment_pkg_notification" to "service_role";

grant truncate on table "public"."payment_pkg_notification" to "service_role";

grant update on table "public"."payment_pkg_notification" to "service_role";

grant delete on table "public"."payment_plan" to "anon";

grant insert on table "public"."payment_plan" to "anon";

grant references on table "public"."payment_plan" to "anon";

grant select on table "public"."payment_plan" to "anon";

grant trigger on table "public"."payment_plan" to "anon";

grant truncate on table "public"."payment_plan" to "anon";

grant update on table "public"."payment_plan" to "anon";

grant delete on table "public"."payment_plan" to "authenticated";

grant insert on table "public"."payment_plan" to "authenticated";

grant references on table "public"."payment_plan" to "authenticated";

grant select on table "public"."payment_plan" to "authenticated";

grant trigger on table "public"."payment_plan" to "authenticated";

grant truncate on table "public"."payment_plan" to "authenticated";

grant update on table "public"."payment_plan" to "authenticated";

grant delete on table "public"."payment_plan" to "service_role";

grant insert on table "public"."payment_plan" to "service_role";

grant references on table "public"."payment_plan" to "service_role";

grant select on table "public"."payment_plan" to "service_role";

grant trigger on table "public"."payment_plan" to "service_role";

grant truncate on table "public"."payment_plan" to "service_role";

grant update on table "public"."payment_plan" to "service_role";

grant delete on table "public"."payment_plan_notification" to "anon";

grant insert on table "public"."payment_plan_notification" to "anon";

grant references on table "public"."payment_plan_notification" to "anon";

grant select on table "public"."payment_plan_notification" to "anon";

grant trigger on table "public"."payment_plan_notification" to "anon";

grant truncate on table "public"."payment_plan_notification" to "anon";

grant update on table "public"."payment_plan_notification" to "anon";

grant delete on table "public"."payment_plan_notification" to "authenticated";

grant insert on table "public"."payment_plan_notification" to "authenticated";

grant references on table "public"."payment_plan_notification" to "authenticated";

grant select on table "public"."payment_plan_notification" to "authenticated";

grant trigger on table "public"."payment_plan_notification" to "authenticated";

grant truncate on table "public"."payment_plan_notification" to "authenticated";

grant update on table "public"."payment_plan_notification" to "authenticated";

grant delete on table "public"."payment_plan_notification" to "service_role";

grant insert on table "public"."payment_plan_notification" to "service_role";

grant references on table "public"."payment_plan_notification" to "service_role";

grant select on table "public"."payment_plan_notification" to "service_role";

grant trigger on table "public"."payment_plan_notification" to "service_role";

grant truncate on table "public"."payment_plan_notification" to "service_role";

grant update on table "public"."payment_plan_notification" to "service_role";

grant delete on table "public"."pkg" to "anon";

grant insert on table "public"."pkg" to "anon";

grant references on table "public"."pkg" to "anon";

grant select on table "public"."pkg" to "anon";

grant trigger on table "public"."pkg" to "anon";

grant truncate on table "public"."pkg" to "anon";

grant update on table "public"."pkg" to "anon";

grant delete on table "public"."pkg" to "authenticated";

grant insert on table "public"."pkg" to "authenticated";

grant references on table "public"."pkg" to "authenticated";

grant select on table "public"."pkg" to "authenticated";

grant trigger on table "public"."pkg" to "authenticated";

grant truncate on table "public"."pkg" to "authenticated";

grant update on table "public"."pkg" to "authenticated";

grant delete on table "public"."pkg" to "service_role";

grant insert on table "public"."pkg" to "service_role";

grant references on table "public"."pkg" to "service_role";

grant select on table "public"."pkg" to "service_role";

grant trigger on table "public"."pkg" to "service_role";

grant truncate on table "public"."pkg" to "service_role";

grant update on table "public"."pkg" to "service_role";

grant delete on table "public"."pkg_profile" to "anon";

grant insert on table "public"."pkg_profile" to "anon";

grant references on table "public"."pkg_profile" to "anon";

grant select on table "public"."pkg_profile" to "anon";

grant trigger on table "public"."pkg_profile" to "anon";

grant truncate on table "public"."pkg_profile" to "anon";

grant update on table "public"."pkg_profile" to "anon";

grant delete on table "public"."pkg_profile" to "authenticated";

grant insert on table "public"."pkg_profile" to "authenticated";

grant references on table "public"."pkg_profile" to "authenticated";

grant select on table "public"."pkg_profile" to "authenticated";

grant trigger on table "public"."pkg_profile" to "authenticated";

grant truncate on table "public"."pkg_profile" to "authenticated";

grant update on table "public"."pkg_profile" to "authenticated";

grant delete on table "public"."pkg_profile" to "service_role";

grant insert on table "public"."pkg_profile" to "service_role";

grant references on table "public"."pkg_profile" to "service_role";

grant select on table "public"."pkg_profile" to "service_role";

grant trigger on table "public"."pkg_profile" to "service_role";

grant truncate on table "public"."pkg_profile" to "service_role";

grant update on table "public"."pkg_profile" to "service_role";

grant delete on table "public"."plan_profile" to "anon";

grant insert on table "public"."plan_profile" to "anon";

grant references on table "public"."plan_profile" to "anon";

grant select on table "public"."plan_profile" to "anon";

grant trigger on table "public"."plan_profile" to "anon";

grant truncate on table "public"."plan_profile" to "anon";

grant update on table "public"."plan_profile" to "anon";

grant delete on table "public"."plan_profile" to "authenticated";

grant insert on table "public"."plan_profile" to "authenticated";

grant references on table "public"."plan_profile" to "authenticated";

grant select on table "public"."plan_profile" to "authenticated";

grant trigger on table "public"."plan_profile" to "authenticated";

grant truncate on table "public"."plan_profile" to "authenticated";

grant update on table "public"."plan_profile" to "authenticated";

grant delete on table "public"."plan_profile" to "service_role";

grant insert on table "public"."plan_profile" to "service_role";

grant references on table "public"."plan_profile" to "service_role";

grant select on table "public"."plan_profile" to "service_role";

grant trigger on table "public"."plan_profile" to "service_role";

grant truncate on table "public"."plan_profile" to "service_role";

grant update on table "public"."plan_profile" to "service_role";

grant delete on table "public"."subscription_asaas" to "anon";

grant insert on table "public"."subscription_asaas" to "anon";

grant references on table "public"."subscription_asaas" to "anon";

grant select on table "public"."subscription_asaas" to "anon";

grant trigger on table "public"."subscription_asaas" to "anon";

grant truncate on table "public"."subscription_asaas" to "anon";

grant update on table "public"."subscription_asaas" to "anon";

grant delete on table "public"."subscription_asaas" to "authenticated";

grant insert on table "public"."subscription_asaas" to "authenticated";

grant references on table "public"."subscription_asaas" to "authenticated";

grant select on table "public"."subscription_asaas" to "authenticated";

grant trigger on table "public"."subscription_asaas" to "authenticated";

grant truncate on table "public"."subscription_asaas" to "authenticated";

grant update on table "public"."subscription_asaas" to "authenticated";

grant delete on table "public"."subscription_asaas" to "service_role";

grant insert on table "public"."subscription_asaas" to "service_role";

grant references on table "public"."subscription_asaas" to "service_role";

grant select on table "public"."subscription_asaas" to "service_role";

grant trigger on table "public"."subscription_asaas" to "service_role";

grant truncate on table "public"."subscription_asaas" to "service_role";

grant update on table "public"."subscription_asaas" to "service_role";

grant delete on table "public"."transaction_detail" to "anon";

grant insert on table "public"."transaction_detail" to "anon";

grant references on table "public"."transaction_detail" to "anon";

grant select on table "public"."transaction_detail" to "anon";

grant trigger on table "public"."transaction_detail" to "anon";

grant truncate on table "public"."transaction_detail" to "anon";

grant update on table "public"."transaction_detail" to "anon";

grant delete on table "public"."transaction_detail" to "authenticated";

grant insert on table "public"."transaction_detail" to "authenticated";

grant references on table "public"."transaction_detail" to "authenticated";

grant select on table "public"."transaction_detail" to "authenticated";

grant trigger on table "public"."transaction_detail" to "authenticated";

grant truncate on table "public"."transaction_detail" to "authenticated";

grant update on table "public"."transaction_detail" to "authenticated";

grant delete on table "public"."transaction_detail" to "service_role";

grant insert on table "public"."transaction_detail" to "service_role";

grant references on table "public"."transaction_detail" to "service_role";

grant select on table "public"."transaction_detail" to "service_role";

grant trigger on table "public"."transaction_detail" to "service_role";

grant truncate on table "public"."transaction_detail" to "service_role";

grant update on table "public"."transaction_detail" to "service_role";

grant delete on table "public"."user_roles" to "anon";

grant insert on table "public"."user_roles" to "anon";

grant references on table "public"."user_roles" to "anon";

grant select on table "public"."user_roles" to "anon";

grant trigger on table "public"."user_roles" to "anon";

grant truncate on table "public"."user_roles" to "anon";

grant update on table "public"."user_roles" to "anon";

grant delete on table "public"."user_roles" to "authenticated";

grant insert on table "public"."user_roles" to "authenticated";

grant references on table "public"."user_roles" to "authenticated";

grant select on table "public"."user_roles" to "authenticated";

grant trigger on table "public"."user_roles" to "authenticated";

grant truncate on table "public"."user_roles" to "authenticated";

grant update on table "public"."user_roles" to "authenticated";

grant delete on table "public"."user_roles" to "service_role";

grant insert on table "public"."user_roles" to "service_role";

grant references on table "public"."user_roles" to "service_role";

grant select on table "public"."user_roles" to "service_role";

grant trigger on table "public"."user_roles" to "service_role";

grant truncate on table "public"."user_roles" to "service_role";

grant update on table "public"."user_roles" to "service_role";

grant delete on table "public"."user_roles" to "supabase_auth_admin";

grant insert on table "public"."user_roles" to "supabase_auth_admin";

grant references on table "public"."user_roles" to "supabase_auth_admin";

grant select on table "public"."user_roles" to "supabase_auth_admin";

grant trigger on table "public"."user_roles" to "supabase_auth_admin";

grant truncate on table "public"."user_roles" to "supabase_auth_admin";

grant update on table "public"."user_roles" to "supabase_auth_admin";

CREATE TRIGGER payment_pkg AFTER INSERT OR UPDATE ON public.payment_pkg FOR EACH ROW EXECUTE FUNCTION new_payment_pkg();

CREATE TRIGGER payment_pkg_notification_trigger AFTER INSERT OR UPDATE ON public.payment_pkg_notification FOR EACH ROW EXECUTE FUNCTION new_payment_pkg_notification();

CREATE TRIGGER payment_plan AFTER INSERT OR UPDATE ON public.payment_plan FOR EACH ROW EXECUTE FUNCTION new_payment_plan();

CREATE TRIGGER payment_plan_notification AFTER INSERT OR UPDATE ON public.payment_plan_notification FOR EACH ROW EXECUTE FUNCTION new_payment_plan_notification();

CREATE TRIGGER enforce_bucket_name_length_trigger BEFORE INSERT OR UPDATE OF name ON storage.buckets FOR EACH ROW EXECUTE FUNCTION storage.enforce_bucket_name_length();

CREATE TRIGGER objects_delete_delete_prefix AFTER DELETE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();

CREATE TRIGGER objects_insert_create_prefix BEFORE INSERT ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.objects_insert_prefix_trigger();

CREATE TRIGGER objects_update_create_prefix BEFORE UPDATE ON storage.objects FOR EACH ROW WHEN (((new.name <> old.name) OR (new.bucket_id <> old.bucket_id))) EXECUTE FUNCTION storage.objects_update_prefix_trigger();

CREATE TRIGGER prefixes_create_hierarchy BEFORE INSERT ON storage.prefixes FOR EACH ROW WHEN ((pg_trigger_depth() < 1)) EXECUTE FUNCTION storage.prefixes_insert_trigger();

CREATE TRIGGER prefixes_delete_hierarchy AFTER DELETE ON storage.prefixes FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();


